# Social Media Sentiment Analysis for Market Impact

A real-time sentiment analysis system that processes social media posts and predicts market impact using advanced NLP and machine learning techniques.

## Features

- **Real-time Data Collection**: Twitter, Reddit, News APIs
- **Advanced Sentiment Analysis**: Ensemble of FinBERT, VADER, and TextBlob
- **Market Impact Prediction**: ML models trained on historical correlations
- **Entity Recognition**: Automatic linking of companies/tickers to posts
- **Real-time Dashboard**: Live sentiment tracking and alerts
- **Scalable Architecture**: Microservices with Kafka streaming

## Quick Start

### Using Docker Compose (Recommended)

```bash
# Clone the repository
git clone https://github.com/yourusername/social-media-sentiment-analysis.git
cd social-media-sentiment-analysis

# Copy environment file
cp .env.example .env
# Edit .env with your API keys

# Start all services
docker-compose up -d

# Access the application
# API: http://localhost:8000
# Dashboard: http://localhost:3000
# Grafana: http://localhost:3001